<?php

namespace app\applent\lists\withdrawal;

use app\applent\lists\BaseApiDataLists;
use app\common\model\withdrawal\WithdrawalRecord;
use app\common\lists\ListsSearchInterface;
use app\common\service\ConfigService;

/**
 * 提现记录列表
 * Class WithdrawalRecordLists
 * @package app\applent\lists\withdrawal
 */
class WithdrawalRecordLists extends BaseApiDataLists implements ListsSearchInterface
{
    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
        $searchFields = ['date','status'];
        //获取两个数组交集
        return array_intersect(array_keys($this->params),$searchFields);
    }

    /**
     * @notes 设置列表字段
     * @return array
     */
    public function setFields(): array
    {
        return [
            'id',
            'withdrawal_no',
            'withdrawal_amount',
            'exchange_amount', 
            'remaining_income',
            'withdrawal_type',
            'account_name',
            'account_number',
            'status',
            'create_time',
        ];
    }

    /**
     * @notes 设置排序
     * @return array
     */
    public function setOrder(): array
    {
        return ['create_time' => 'desc'];
    }

    /**
     * @notes 获取列表数据
     * @return array
     */
    public function lists(): array
    {
        $lists = WithdrawalRecord::field($this->setFields())
            ->where(['user_id' => $this->userId])
            ->withSearch($this->setSearch(), $this->params)
            ->order($this->setOrder())
            ->page($this->pageNo,  $this->pageSize)
            ->append(['withdrawal_type_text', 'status_text', 'account_number_masked'])
            ->select()
            ->toArray();

        return $this->createReturn($lists);
    }

    /**
     * @notes 获取数量
     * @return int
     */
    public function count(): int
    {
        return WithdrawalRecord::where(['user_id' => $this->userId])
            ->withSearch($this->setSearch(), $this->params)
            ->count();
    }

    /**
     * @notes 处理列表数据
     * @param array $lists
     * @return array
     */
    public function createReturn(array $lists): array
    {
        $data = [];

        foreach ($lists as $item) {
            $data[] = [
                'id' => $item['id'],
                'withdrawal_no' => $item['withdrawal_no'],
                'withdrawal_amount' => number_format($item['withdrawal_amount'], 2),
                'exchange_amount' => number_format($item['exchange_amount'], 2).ConfigService::get('systemconfig', 'profit_name', 0),
                'remaining_income' => number_format($item['remaining_income'], 2).ConfigService::get('systemconfig', 'profit_name', 0),
                'withdrawal_type' => $item['withdrawal_type'],
                'withdrawal_type_text' => $item['withdrawal_type_text'],  // 使用模型获取器
                'account_name' => $item['account_name'],
                'account_number' => $item['account_number_masked'],  // 使用模型获取器
                'status' => $item['status'],
                'status_text' => $item['status_text'],  // 使用模型获取器
                'create_time' => $item['create_time'],
            ];
        }

        return $data;
    }
}
