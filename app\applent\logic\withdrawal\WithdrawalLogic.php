<?php

namespace app\applent\logic\withdrawal;

use app\common\logic\BaseLogic;
use app\common\model\withdrawal\WithdrawalConfig;
use app\common\model\withdrawal\WithdrawalRecord;
use app\common\model\user\UserBalance;
use app\common\model\user\UserIncomeLog;
use app\common\model\bank\UserBankCard;
use app\common\model\bank\UserAlipay;
use app\common\model\bank\BankCard;
use app\common\service\ConfigService;
use think\facade\Db;
use think\facade\Cache;

/**
 * 提现逻辑
 * Class WithdrawalLogic
 * @package app\applent\logic\withdrawal
 */
class WithdrawalLogic extends BaseLogic
{
    /**
     * @notes 获取提现金额配置列表
     * @return array|false
     */
    public static function getWithdrawalConfigList()
    {
        $data = WithdrawalConfig::field('id,money,exchange_num,sort')
            ->order('sort desc, id asc')
            ->select()
            ->toArray();
        foreach ($data as $key => $value) {
            $data[$key]['money_text'] = number_format($value['money'], 2) . '元';
            $data[$key]['exchange_text'] = $value['exchange_num'] .  ConfigService::get('systemconfig', 'profit_name', 0);
        }
        return $data;
    }

    /**
     * @notes 申请提现
     * @param array $params
     * @return bool
     */
    public static function applyWithdrawal($params)
    {
        Db::startTrans();
        try {
            // 1. 验证提现配置
            $config = WithdrawalConfig::where('id', $params['config_id'])->find();
            if (!$config) {
                throw new \Exception('提现配置不存在');
            }

            // 2. 验证参数匹配
            if ($config['money'] != $params['withdrawal_amount'] || $config['exchange_num'] != $params['exchange_amount']) {
                throw new \Exception('提现金额与配置不匹配');
            }
            // 3. 检查今日是否已提现
            if (WithdrawalRecord::checkTodayWithdrawal($params['user_id'])) {
                throw new \Exception('每天只能提现一次');
            }

            // 4. 检查用户收益是否足够
            $userBalance = UserBalance::getUserBalance($params['user_id']);
            if (!$userBalance || $userBalance['income'] < $params['exchange_amount']) {
                throw new \Exception('您的收益不足，无法提现');
            }

            // 5. 验证提现方式和账户信息
            $accountInfo = self::validateWithdrawalAccount($params['user_id'], $params['withdrawal_type']);
            if (!$accountInfo) {
                throw new \Exception('请先绑定提现账户');
            }

            // 6. 扣除用户收益
            $beforeIncome = $userBalance['income'];
            $result = UserBalance::where(['user_id' => $params['user_id']])
                ->dec('income', $params['exchange_amount'])
                ->update(['update_time' => time()]);

            if (!$result) {
                throw new \Exception('扣除收益失败');
            }

            $afterIncome = $beforeIncome - $params['exchange_amount'];

            // 6. 生成提现申请单号
            $withdrawalNo = order_sn('W');

            // 7. 记录收益变动日志
            UserIncomeLog::addWithdrawApplyLog(
                $params['user_id'],
                $beforeIncome,
                $afterIncome,
                $params['exchange_amount'],
                $withdrawalNo
            );

            // 8. 创建提现申请记录
            $withdrawalData = [
                'user_id' => $params['user_id'],
                'withdrawal_no' => $withdrawalNo,
                'config_id' => $params['config_id'],
                'withdrawal_amount' => $params['withdrawal_amount'],
                'exchange_amount' => $params['exchange_amount'],
                'remaining_income' => $afterIncome, // 提现后剩余收益
                'withdrawal_type' => $params['withdrawal_type'],
                'account_name' => $accountInfo['account_name'],
                'account_number' => $accountInfo['account_number'],
            ];

            if ($params['withdrawal_type'] == WithdrawalRecord::TYPE_BANK_CARD) {
                $withdrawalData['bank_card_id'] = $accountInfo['id'];
            } else {
                $withdrawalData['alipay_id'] = $accountInfo['id'];
            }

            $withdrawalRecord = WithdrawalRecord::createWithdrawalRecord($withdrawalData);
            if (!$withdrawalRecord) {
                throw new \Exception('提现申请失败');
            }

            Db::commit();
            return true;

        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 验证提现账户信息
     * @param int $userId 用户ID
     * @param int $withdrawalType 提现方式：1=银行卡，2=支付宝
     * @return array|false
     */
    private static function validateWithdrawalAccount($userId, $withdrawalType)
    {
        if ($withdrawalType == WithdrawalRecord::TYPE_BANK_CARD) {
            // 验证银行卡
            $bankCard = UserBankCard::with(['bankCard' => function($query) {
                $query->field('id,name,status');
            }])
            ->where('user_id', $userId)
            ->find();

            if (!$bankCard) {
                throw new \Exception('请先绑定银行卡');
            }

            // 检查银行卡状态
            if (!$bankCard['bankCard'] || $bankCard['bankCard']['status'] != 1) {
                throw new \Exception('该银行卡已被禁用，无法提现');
            }

            return [
                'id' => $bankCard['id'],
                'account_name' => $bankCard['cardholder_name'],
                'account_number' => $bankCard['card_number'],
            ];

        } elseif ($withdrawalType == WithdrawalRecord::TYPE_ALIPAY) {
            // 验证支付宝
            $alipay = UserAlipay::where('user_id', $userId)->find();

            if (!$alipay) {
                throw new \Exception('请先绑定支付宝账户');
            }

            return [
                'id' => $alipay['id'],
                'account_name' => $alipay['real_name'],
                'account_number' => $alipay['alipay_account'],
            ];
        }

        return false;
    }
}