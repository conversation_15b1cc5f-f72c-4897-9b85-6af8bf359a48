<?php

namespace app\applent\controller\recharge;

use app\applent\controller\BaseApiController;
use app\applent\logic\recharge\RechargeLogic;
use app\applent\validate\recharge\RechargeValidate;
use app\common\model\pay\PayConfig;

/**
 * 充值控制器
 * Class RechargeController
 * @package app\applent\controller\recharge
 */
class RechargeController extends BaseApiController
{
    public array $notNeedLogin = ['pay_callback'];

    /**
     * @notes 获取充值套餐列表
     * @return \think\response\Json
     */
    public function get_package_list()
    {
        $result = RechargeLogic::getPackageList();
        if (false === $result) {
            return $this->fail(RechargeLogic::getError());
        }
        return $this->success('获取成功', $result, 1, 0);
    }

    /**
     * @notes 获取支付方式列表
     * @return \think\response\Json
     */
    public function get_pay_methods()
    {
        $result = RechargeLogic::getEnabledPayMethods();
        if (false === $result) {
            return $this->fail(RechargeLogic::getError());
        }
        return $this->success('获取成功', $result, 1, 0);
    }

    /**
     * @notes 创建充值订单
     * @return \think\response\Json
     */
    public function create_order()
    {
        $params = (new RechargeValidate())->post()->goCheck('create_order');
        $result = RechargeLogic::createOrder($this->userId, $params);
        if (false === $result) {
            return $this->fail(RechargeLogic::getError());
        }
        return $this->success('订单创建成功', $result, 1, 0);
    }

    /**
     * @notes 支付回调
     * @return \think\response\Json
     */
    public function pay_callback()
    {
        //接收回调数据
        $json = file_get_contents('php://input');
        $post = json_decode($json, true);

        $result = RechargeLogic::handlePayCallback($post);
        var_dump($result);die;
        if (false === $result) {
            return $this->fail(RechargeLogic::getError());
        }
        return $this->success('支付回调处理成功', $result, 1, 0);
    }
}
