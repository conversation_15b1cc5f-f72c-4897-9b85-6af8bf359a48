<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:56:"themes/admin_simpleboot3/admin\user_money_log\index.html";i:1739780454;s:75:"D:\phpstudy_pro\WWW\demo\public\themes\admin_simpleboot3\public\header.html";i:1739780454;}*/ ?>
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <!-- Set render engine for 360 browser -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- HTML5 shim for IE8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <![endif]-->


    <link href="/themes/admin_simpleboot3/public/assets/themes/<?php echo cmf_get_admin_style(); ?>/bootstrap.min.css" rel="stylesheet">
    <link href="/themes/admin_simpleboot3/public/assets/simpleboot3/css/simplebootadmin.css" rel="stylesheet">
    <link href="/static/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
    <style>
        form .input-order {
            margin-bottom: 0px;
            padding: 0 2px;
            width: 42px;
            font-size: 12px;
        }

        form .input-order:focus {
            outline: none;
        }

        .table-actions {
            margin-top: 5px;
            margin-bottom: 5px;
            padding: 0px;
        }

        .table-list {
            margin-bottom: 0px;
        }

        .form-required {
            color: red;
        }
    </style>
    <script type="text/javascript">
        //全局变量
        var GV = {
            ROOT: "/",
            WEB_ROOT: "/",
            JS_ROOT: "static/js/",
            APP: '<?php echo \think\Request::instance()->module(); ?>'/*当前应用名*/
        };
    </script>
    <script src="/themes/admin_simpleboot3/public/assets/js/jquery-1.10.2.min.js"></script>
    <script src="/static/js/layer/layer.js" rel="stylesheet"></script>
    <script src="/static/js/wind.js"></script>
    <script src="/themes/admin_simpleboot3/public/assets/js/bootstrap.min.js"></script>
    <script>
        Wind.css('artDialog');
        Wind.css('layer');
        $(function () {
            $("[data-toggle='tooltip']").tooltip();
            $("li.dropdown").hover(function () {
                $(this).addClass("open");
            }, function () {
                $(this).removeClass("open");
            });
        });
    </script>
    <?php if(APP_DEBUG): ?>
        <style>
            #think_page_trace_open {
                z-index: 9999;
            }
        </style>
    <?php endif; ?>
<style>
    .gift-img{width:50px;height:50px}
    .gift-img img{width:100%;height:100%;}
    .gift-in input{width:25px;}
    .js-ajax-form{margin-top:30px;}
    .identity img{width:30px;height:30px;border-radius: 50%;}
    .details{cursor: pointer;}
    .layui-layer-demo .layui-layer-title{
        background: #e0e0e0!important;
    }
    .form-control{width:110px!important;}
    #status,#type{    width: 100px;
        height: 32px;
        border-color: #dce4ec;
        color: #aeb5bb;}
    .table-list{font-size:14px!important;}
    .UserMoneyLog-col{color:#ff41ee;}
    .layui-layer{width: 1000px!important;}
    a{text-decoration: none;}
    .details_type td{text-align: center;}
    .UserMoneyLog_count{width:100%;height:40px;line-height: 40px}
    .UserMoneyLog_count span{margin-left:30px;}
</style>
</head>
<body>
<div class="wrap js-check-wrap">
    <ul class="nav nav-tabs">
        <li class="active"><a href="javascript:;">用户资金明细</a></li>
    </ul>
    <form class="well form-inline margin-top-20" name="form1" method="post">
        用户ID：
        <input class="form-control" type="text" name="uid" style="width: 200px;" value="<?php echo input('request.uid'); ?>"
               placeholder="请输入用户ID">
        用户收益ID：
        <input class="form-control" type="text" name="touid" style="width: 200px;" value="<?php echo input('request.touid'); ?>"
               placeholder="请输入用户ID">
         排序：
        <select name="order" id="type">
            <option value="0">时间降序</option>
            <option value="1" <?php if($request['order'] == 1): ?> selected="selected" <?php endif; ?>>变更资金降序</option>
            <option value="2" <?php if($request['order'] == 2): ?> selected="selected" <?php endif; ?>>变更资金升序</option>
        </select>
        变更资金类型：
        <select name="coin_type" id="type">
            <option value="0">全部</option>
            <option value="1" <?php if($request['coin_type'] == '1'): ?> selected="selected" <?php endif; ?>>coin</option>
            <option value="2" <?php if($request['coin_type'] == '2'): ?> selected="selected" <?php endif; ?>>income</option>
            <option value="3" <?php if($request['coin_type'] == '3'): ?> selected="selected" <?php endif; ?>>邀请收益</option>
            <option value="4" <?php if($request['coin_type'] == '4'): ?> selected="selected" <?php endif; ?>>vip</option>
        </select>
        变更类型：
        <select name="type" id="type">
            <option value="0">全部</option>
            <option value="1" <?php if($request['type'] == 1): ?> selected="selected" <?php endif; ?>>充值/收益</option>
            <option value="2" <?php if($request['type'] == 2): ?> selected="selected" <?php endif; ?>>消费</option>
        </select>
        时间:
        <input type="text" class="form-control js-bootstrap-datetime" name="start_time"
               value="<?php echo (isset($request['start_time']) && ($request['start_time'] !== '')?$request['start_time']:''); ?>"
               style="width: 200px;" autocomplete="off">-
        <input type="text" class="form-control js-bootstrap-datetime" name="end_time"
               value="<?php echo (isset($request['end_time']) && ($request['end_time'] !== '')?$request['end_time']:''); ?>"
               style="width: 200px;" autocomplete="off"> &nbsp; &nbsp;

        <input type="submit" class="btn btn-primary" value="搜索" onclick='form1.action="<?php echo url('UserMoneyLog/index'); ?>";form1.submit();'/>
        <a class="btn btn-danger" href="<?php echo url('UserMoneyLog/index'); ?>">清空</a>

        <input type="button" class="btn btn-primary from_export" style="background-color: #1dccaa;" value="导出" onclick='form1.action="<?php echo url('UserMoneyLog/export'); ?>";form1.submit();'>


    </form>

    <?php 

        $type = array(1 => '充值/收益',-1 => '消费');
        $coin_type = array('coin' => '金币','income' => '收益' ,'invitation_coin' => '邀请收益' ,''=>'异常','vip'=>'vip');

     ?>
    <form class="js-ajax-form" action="<?php echo url('UserMoneyLog/upd'); ?>" method="post">
<!--        <h4>总变更<?php echo $total; ?></h4>-->
        <table class="table table-hover table-bordered table-list">
            <thead>
            <tr>
                <th>ID</th>
                <th>用户（ID）</th>
                <th>收益用户（ID）</th>
                <th>变更资金</th>
                <th>变更前资金</th>
                <th>变更后资金</th>
                <th>变更资金类型</th>
                <th>变更类型</th>
                <th>变更原因</th>
                <th>变更时间</th>
            </tr>
            </thead>
            <tfoot>

            <?php if(is_array($data) || $data instanceof \think\Collection || $data instanceof \think\Paginator): if( count($data)==0 ) : echo "" ;else: foreach($data as $key=>$vo): ?>
                <tr>
                    <td><?php echo $vo['log_id']; ?></td>
                    <td><?php echo $vo['uname']; ?>(<?php echo $vo['user_id']; ?>)</td>
                    <td><?php echo $vo['to_user_id']; ?></td>
                    <td><?php echo $vo['change_value']; ?></td>
                    <td><?php echo $vo['change_before']; ?></td>
                    <th><?php echo $vo['change_after']; ?></th>
                    <th><?php echo $coin_type[$vo['coin_type']]; ?></th>
                    <th><?php echo $type[$vo['type']]; ?></th>
                    <th><?php echo $vo['cause']; ?></th>
                    <th><?php echo date("Y-m-d H:i:s",$vo['create_time'] ); ?></th>
                </tr>
            <?php endforeach; endif; else: echo "" ;endif; ?>
            </tfoot>
        </table>
        <ul class="pagination"><?php echo $page; ?></ul>

    </form>

</div>
<script src="/static/js/layer/layer.js" rel="stylesheet"></script>
<script src="/static/js/admin.js"></script>
<script>
    $(".details").click(function(){
        var id=$(this).attr("data-id");
        $.ajax({
            url: "<?php echo url('UserMoneyLog/select_call'); ?>",
            type: 'get',
            dataType: 'json',
            offset: 'rb', //具体配置参考：offset参数项
            area: ['1000px', '500px'],
            data: {id: id},
            success: function (data) {

                var html='<div style="width:1000px!important;height:500px;"><div class="UserMoneyLog_count"><span>总消费(钻石)：'+data.coin+'</span><span>主播总收益： '+data.profit+' </span><span>邀请总收益 ： '+data.money+'<span><span>总时长 ： '+data.time+'<span></div><table class="table table-hover table-bordered details_type"><thead><tr><th style="text-align: center;background: #f7f7f7;">消费用户（ID）</th><th style="text-align: center;background: #f7f7f7;">收益用户（ID）</th><th style="text-align: center;background: #f7f7f7;">消费数量（钻石）</th><th style="text-align: center;background: #f7f7f7;">主播收益</th><th style="text-align: center;background: #f7f7f7;">主播邀请人(ID)</th><th style="text-align: center;background: #f7f7f7;">邀请收益(元)</th><th style="text-align: center;background: #f7f7f7;">消费说明</th><th style="text-align: center;background: #f7f7f7;">消费时间</th></tr></thead><tbody>';
               var user=data.user;
                for(var i=0;i<user.length;i++){
                    html+='<tr><td>'+user[i]['uname']+'('+user[i]['user_id']+')</td><td>'+user[i]['toname']+'('+user[i]['to_user_id']+')</td><td>'+user[i]['coin']+'</td><td>'+user[i]['profit']+'</td><td>';
                    if(user[i]['cid']){
                         html+=user[i]['cname']+'('+user[i]['cid']+')</td><td>'+user[i]['money'];
                    }else{
                         html+='</td><td>';
                    }
                    html+='</td><td>'+user[i]['content']+'</td><td>'+user[i]['create_time']+'</td></tr>';

                }
                html+='</tbody></table></div>';
                 //自定页
                 layer.open({
                    type: 1,
                    title: '视频通话记录',
                    closeBtn: 0,
                    shadeClose: true,
                    skin: 'yourclass',
                    content: html
                });

            }
        });



    })


</script>
</body>
</html>
