<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:46:"themes/admin_simpleboot3/admin\gift\index.html";i:1739780454;s:75:"D:\phpstudy_pro\WWW\demo\public\themes\admin_simpleboot3\public\header.html";i:1739780454;}*/ ?>
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <!-- Set render engine for 360 browser -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- HTML5 shim for IE8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <![endif]-->


    <link href="/themes/admin_simpleboot3/public/assets/themes/<?php echo cmf_get_admin_style(); ?>/bootstrap.min.css" rel="stylesheet">
    <link href="/themes/admin_simpleboot3/public/assets/simpleboot3/css/simplebootadmin.css" rel="stylesheet">
    <link href="/static/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
    <style>
        form .input-order {
            margin-bottom: 0px;
            padding: 0 2px;
            width: 42px;
            font-size: 12px;
        }

        form .input-order:focus {
            outline: none;
        }

        .table-actions {
            margin-top: 5px;
            margin-bottom: 5px;
            padding: 0px;
        }

        .table-list {
            margin-bottom: 0px;
        }

        .form-required {
            color: red;
        }
    </style>
    <script type="text/javascript">
        //全局变量
        var GV = {
            ROOT: "/",
            WEB_ROOT: "/",
            JS_ROOT: "static/js/",
            APP: '<?php echo \think\Request::instance()->module(); ?>'/*当前应用名*/
        };
    </script>
    <script src="/themes/admin_simpleboot3/public/assets/js/jquery-1.10.2.min.js"></script>
    <script src="/static/js/layer/layer.js" rel="stylesheet"></script>
    <script src="/static/js/wind.js"></script>
    <script src="/themes/admin_simpleboot3/public/assets/js/bootstrap.min.js"></script>
    <script>
        Wind.css('artDialog');
        Wind.css('layer');
        $(function () {
            $("[data-toggle='tooltip']").tooltip();
            $("li.dropdown").hover(function () {
                $(this).addClass("open");
            }, function () {
                $(this).removeClass("open");
            });
        });
    </script>
    <?php if(APP_DEBUG): ?>
        <style>
            #think_page_trace_open {
                z-index: 9999;
            }
        </style>
    <?php endif; ?>
<style>
    .gift-img {
        width: 50px;
        height: 50px
    }

    .gift-img img {
        width: 100%;
        height: 100%;
    }

    .gift-in input {
        width: 30px;
    }

    .js-ajax-form {
        margin-top: 30px;
    }
</style>
</head>
<body>
<div class="wrap js-check-wrap">
    <ul class="nav nav-tabs">
        <li class="active"><a href="javascript:;">礼物列表</a></li>
        <li><a href="<?php echo url('gift/add'); ?>">添加礼物</a></li>
    </ul>

    <form class="js-ajax-form" action="<?php echo url('gift/upd'); ?>" method="post">

        <table class="table table-hover table-bordered table-list">
            <thead>
            <tr>
                <th>排序</th>
                <th>ID</th>
                <th>礼物名称</th>
                <th>礼物类型</th>
                <th>礼物赠送类型</th>
                <th>全频道推送</th>
<!--                <th>是否是搭讪礼物</th>-->
                <th><?php echo $currency_name; ?>数</th>
                <th>图片</th>
                <th>动画文件链接</th>
                <th>添加时间</th>
                <th>操作</th>
            </tr>
            </thead>
            <tfoot>
            <?php if(is_array($gift) || $gift instanceof \think\Collection || $gift instanceof \think\Paginator): if( count($gift)==0 ) : echo "" ;else: foreach($gift as $key=>$vo): ?>
                <tr>
                    <td class="gift-in"><input type="text" name="listorders[<?php echo $vo['id']; ?>]" value="<?php echo $vo['orderno']; ?>"></td>
                    <td><?php echo $vo['id']; ?></td>
                    <td><?php echo $vo['name']; ?></td>
                    <td>
                        <?php if($vo['type'] == '1'): ?>
                            普通礼物
                            <?php elseif($vo['type'] == '2'): ?>
                            守护礼物
                            <?php else: ?>
                            动画svge
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if($vo['gift_type'] == '1'): ?>
                            普通赠送
                            <?php elseif($vo['gift_type'] == '2'): ?>
                            连续赠送
                            <?php else: ?>
                            动画svge
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if($vo['is_all_notify'] == '1'): ?>
                            是
                            <?php else: ?>
                            否
                        <?php endif; ?>
                    </td>
<!--                    <td>-->
<!--                        <?php if($vo['is_accost'] == 1): ?>-->
<!--                            是-->
<!--                            <?php else: ?>-->
<!--                            否-->
<!--                        <?php endif; ?>-->
<!--                    </td>-->
                    <td><?php echo $vo['coin']; ?></td>
                    <td class="gift-img"><img src="<?php echo $vo['img']; ?>" alt=""></td>
                    <td style="text-align: center;">
                        <?php if($vo['svga']): ?>
                            <a href="javescript:;" class="btn-main" data-url="<?php echo $vo['svga']; ?>">复制</a>
                        <?php endif; ?>
                    </td>
                    <td><?php echo date("Y-m-d H:i:s",$vo['addtime'] ); ?></td>
                    <td>
                        <a href="<?php echo url('gift/add',array('id'=>$vo['id'])); ?>">编辑</a> |
                        <a href="javescript:;" class="del" data-id="<?php echo $vo['id']; ?>">删除</a>
                    </td>
                </tr>
            <?php endforeach; endif; else: echo "" ;endif; ?>
            </tfoot>
        </table>
        <button type="button" class="btn btn-primary" style="margin-top:20px;"> 排 序</button>
    </form>

</div>
<script src="/static/js/layer/layer.js" rel="stylesheet"></script>
<script src="/static/js/clipboard.min.js" type="text/javascript"></script>
<script>
    var url = '';
    $('.btn-main').click(function(){
        url = $(this).attr('data-url');
    })
    var clipboard = new ClipboardJS('.btn-main', {
        // 点击copy按钮，直接通过text直接返回复印的内容
        text: function() {
            return url;
        }
    });

    clipboard.on('success', function(e) {
        console.log(e);
        layer.msg('复制成功');
    });

    clipboard.on('error', function(e) {
        console.log(e);
    });

    $(".del").click(function () {
        var id = $(this).attr('data-id');
        layer.confirm('确定删除？', {
            btn: ['确定', '取消'] //按钮
        }, function () {
            $.ajax({
                url: "<?php echo url('gift/del'); ?>",
                type: 'post',
                dataType: 'json',
                data: {id: id},
                success: function (data) {
                    if (data == '1') {
                        layer.msg("删除成功", {time: 2000, icon: 1}, function () {
                            window.location.reload();
                        });
                    } else {
                        layer.msg(lang('Delete_failed'), {time: 2000, icon: 2});
                    }
                }
            });

        });
    })
    $(".btn-primary").click(function () {
        $(".js-ajax-form").submit();
    })
</script>
</body>
</html>
