<?php

namespace app\applent\validate\recharge;

use app\common\validate\BaseValidate;

/**
 * 充值验证器
 * Class RechargeValidate
 * @package app\applent\validate\recharge
 */
class RechargeValidate extends BaseValidate
{
    protected $rule = [
        // 创建订单
        'package_id' => 'require|integer',
        //支付方式
        'pay_method_id' => 'require',
    ];

    protected $message = [
        // 创建订单错误信息
        'package_id.require' => '请选择充值套餐',
        'package_id.integer' => '套餐ID格式错误',
        //支付方式错误信息
        'pay_method_id.require' => '请选择支付方式',
    ];

    /**
     * @notes 创建订单场景
     * @return RechargeValidate
     */
    public function sceneCreateOrder()
    {
        return $this->only(['package_id','pay_method_id']);
    }
}
