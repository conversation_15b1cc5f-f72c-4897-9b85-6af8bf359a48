<?php


namespace app\common\model\user;


use app\common\enum\user\UserEnum;
use app\common\model\BaseModel;
use app\common\service\FileService;
use think\model\concern\SoftDelete;
use app\common\service\ConfigService;
use app\common\model\userFollow\UserFollow;
use app\common\model\user\UserPhotoWall;
use app\common\model\user\UserCareer;
use app\common\model\user\UserLabel;
use app\common\model\user\UserImageLabel;
use app\common\model\level\Level;
use app\common\model\live\LiveGift;
use app\common\model\user\UserVisitor;
use app\common\model\user\UserDataReview;
use app\common\model\user\UserDynamic;
use app\common\model\user\UserDynamicComment;
use app\common\model\user\UserDynamicLike;
use app\common\model\user\UserBalance;
use think\facade\Db;
/**
 * 用户模型
 * Class User
 * @package app\common\model\user
 */
class User extends BaseModel
{
    use SoftDelete;

    protected $deleteTime = 'delete_time';

    // 注意：不在这里全局定义 append 字段，而是在需要时动态添加


    /**
     * @notes 关联用户授权模型
     * @return \think\model\relation\HasOne
     */
    public function userAuth()
    {
        return $this->hasOne(UserAuth::class, 'user_id');
    }


    /**
     * @notes 搜索器-用户信息
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchKeywordAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('nickname|id|mobile', 'like', '%' . $value . '%');
        }
    }


    /**
     * @notes 搜索器-注册来源
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchChannelAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('channel', '=', $value);
        }
    }


    /**
     * @notes 搜索器-注册时间
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchCreateTimeStartAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('create_time', '>=', strtotime($value));
        }
    }


    /**
     * @notes 搜索器-注册时间
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchCreateTimeEndAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('create_time', '<=', strtotime($value));
        }
    }

    /**
     * @notes 搜索器-在线状态
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchOnlineStatusAttr($query, $value, $data)
    {
        if (!empty($value)) {
            switch (intval($value)) {
                case 1: // 离线
                    $query->where('is_online', '=', 0);
                    break;
                case 2: // 在聊（忙碌状态）
                    $query->where('is_busy', '=', 1);
                    break;
                case 3: // 在线
                    $query->where('is_online', '=', 1)
                          ->where('is_busy', '=', 0);
                    break;
            }
        }
    }

    /**
     * @notes 搜索器-最小年龄
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchMinAgeAttr($query, $value, $data)
    {
        if (!empty($value) && is_numeric($value)) {
            $query->where('age', '>=', intval($value));
        }
    }

    /**
     * @notes 搜索器-最大年龄
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchMaxAgeAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('age', '<=', $value);
        }
    }

    /**
     * @notes 搜索器-最小价格（只查video_price字段）
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchMinPriceAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('video_price', '>=', $value);
        }
    }

    /**
     * @notes 搜索器-最大价格（只查video_price字段）
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchMaxPriceAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('video_price', '<=', $value);
        }
    }


    /**
     * @notes 头像获取器 - 用于头像地址拼接域名
     * @param $value
     * @return string
     */
    public function getAvatarAttr($value)
    {
        return (!empty($value) && is_string($value)) ? FileService::getFileUrl(trim($value)) : '';
    }

    /**
     * @notes 缩略图获取器 - 用于缩略图地址拼接域名
     * @param $value
     * @return string
     */
    public function getThumbAvatarAttr($value)
    {
        return (!empty($value) && is_string($value)) ? FileService::getFileUrl(trim($value)) : '';
    }

    /**
     * @notes 获取器-性别描述
     * @param $value
     * @param $data
     * @return string|string[]
     */
    // public function getSexAttr($value, $data)
    // {
    //     return UserEnum::getSexDesc($value);
    // }


    /**
     * @notes 登录时间
     * @param $value
     * @return string
     */
    public function getLoginTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * @notes 生成用户编码
     * @param string $prefix
     * @param int $length
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function createUserSn($prefix = '', $length = 8)
    {
        $rand_str = '';
        for ($i = 0; $i < $length; $i++) {
            $rand_str .= mt_rand(1, 9);
        }
        $sn = $prefix . $rand_str;
        // if (User::where(['sn' => $sn])->find()) {
        //     return self::createUserSn($prefix, $length);
        // }
        return $sn;
    }

    public function getVideoPriceAttr($value)
    {
        $minPrice = ConfigService::get('systemconfig', 'min_video_price');
        return $value > 0 ? $value : $minPrice;
    }

    public function getVoicePriceAttr($value)
    {
        $minPrice = ConfigService::get('systemconfig', 'min_voice_price');
        return $value > 0 ? $value : $minPrice;
    }

    /**
     * @notes 照片墙获取器
     * @param $value
     * @param $data
     * @return array
     */
    public function getPhotoWallAttr($value, $data)
    {
        return UserPhotoWall::getUserPhotoWall($data['id']);
    }

    /**
     * @notes 通话费用获取器
     * @param $value
     * @param $data
     * @return array
     */
    public function getCallPricesAttr($value, $data)
    {
        // 视频通话费用
        $videoPrice = $data['video_price'];
        if ($videoPrice <= 0) {
            $videoPrice = ConfigService::get('systemconfig', 'min_video_price', 0);
        }

        // 语音通话费用
        $voicePrice = $data['voice_price'];
        if ($voicePrice <= 0) {
            $voicePrice = ConfigService::get('systemconfig', 'min_voice_price', 0);
        }

        $currency_name = ConfigService::get('systemconfig', 'currency_name', '金币').'/min';
        return [
            'video_price' => $videoPrice.$currency_name,
            'voice_price' => $voicePrice.$currency_name
        ];
    }

    /**
     * @notes 形象标签获取器
     * @param $value
     * @param $data
     * @return array
     */
    public function getImageLabelsAttr($value, $data)
    {
        if (!isset($data['id']) || !$data['id']) {
            return [];
        }

        // 通过关联表查询用户的形象标签
        $label_id = UserLabel::where('user_id', $data['id'])
            ->column('image_label');

        $relations = UserImageLabel::where('id', 'in', $label_id)
            ->select();

        $result = [];
        foreach ($relations as $relation) {
            $result[] = [
                'id' => $relation['id'],
                'name' => $relation['name'],
                'color' => $relation['color'] ?? '#000000' // 默认颜色
            ];
        }
        return $result;
    }

    /**
     * @notes 等级信息获取器
     * @param $value
     * @param $data
     * @return array
     */
    public function getLevelInfoAttr($value, $data)
    {
        if (!isset($data['level_id']) || !$data['level_id']) {
            return [
                'levelid' => 0,
                'name' => '',
                'level_bg' => ''
            ];
        }

        $level = Level::where(['levelid' => $data['level_id']])->find();
        if (!$level) {
            return [
                'levelid' => 0,
                'name' => '',
                'level_bg' => ''
            ];
        }

        if($data['sex'] == 1){
            $level_bg = $level['level_bg'];
        }else{
            $level_bg = $level['anchor_bg'];
        }

        return [
            'levelid' => $level['levelid'],
            'name' => $level['level_name'],
            'level_bg' => $level_bg
        ];
    }

    /**
     * @notes 礼物墙获取器
     * @param $value
     * @param $data
     * @return array
     */
    public function getGiftWallAttr($value, $data)
    {
        if (!isset($data['id']) || !$data['id']) {
            return [];
        }

        // 获取用户收到的礼物统计，使用MAX获取prop_name避免GROUP BY错误
        $result = LiveGift::where(['to_user_id' => $data['id']])
            ->field('prop_id as gift_id, MAX(prop_name) as gift_name, sum(num) as total_count,gift_image')
            ->group('prop_id')
            ->order('total_score desc')
            ->select()
            ->toArray();
        return $result;
    }

    /**
     * @notes 用户统计数据获取器
     * @param $value
     * @param $data
     * @return array
     */
    public function getUserStatsAttr($value, $data)
    {
        if (!isset($data['id']) || !$data['id']) {
            return [
                'follow_count' => 0,
                'fans_count' => 0,
                'gift_count' => 0,
                'visitor_count' => 0,
                'dynamic_count' => 0,
                'comment_count' => 0,
                'like_count' => 0
            ];
        }

        try {
            $where[] = ['status', 'in', [1, 3]];
            // 1. 获取关注人数量（我关注的人数）
            $followCount = UserFollow::where([
                'user_id' => $data['id']
            ])->where($where)->count();

            // 2. 获取粉丝数量（关注我的人数）
            $fansCount = UserFollow::where([
                'to_user_id' => $data['id']
            ])->where($where)->count();

            // 3. 获取收礼数量（收到的礼物总数）
            $giftCount = LiveGift::where([
                'to_user_id' => $data['id']
            ])->sum('num') ?: 0;

            // 4. 获取访客数量
            $visitorCount = UserVisitor::where('to_user_id', $data['id'])->count();

            // 5. 获取动态数量（已审核通过的动态）
            $dynamicCount = UserDynamic::where([
                'user_id' => $data['id'],
                'status' => 1  // 只统计审核通过的动态
            ])->where('delete_time', null)->count();

            // 6. 获取评论数量（该用户所有动态收到的评论总数）
            $userDynamicIds = UserDynamic::where([
                'user_id' => $data['id'],
                'status' => 1  // 只统计审核通过的动态
            ])->where('delete_time', null)->column('id');

            $commentCount = 0;
            if (!empty($userDynamicIds)) {
                $commentCount = UserDynamicComment::where('dynamic_id', 'in', $userDynamicIds)->count();
            }

            // 7. 获取点赞数量（该用户所有动态收到的点赞总数）
            $likeCount = 0;
            if (!empty($userDynamicIds)) {
                $likeCount = UserDynamicLike::where('dynamic_id', 'in', $userDynamicIds)->count();
            }

            return [
                'follow_count' => $followCount,
                'fans_count' => $fansCount,
                'gift_count' => $giftCount,
                'visitor_count' => $visitorCount,
                'dynamic_count' => $dynamicCount,
                'comment_count' => $commentCount,
                'like_count' => $likeCount
            ];

        } catch (\Exception $e) {
            // 如果统计出错，返回默认值
            return [
                'follow_count' => 0,
                'fans_count' => 0,
                'gift_count' => 0,
                'visitor_count' => 0,
                'dynamic_count' => 0,
                'comment_count' => 0,
                'like_count' => 0
            ];
        }
    }

    /**
     * @notes 头像信息获取器（包含审核状态）
     * @param $value
     * @param $data
     * @return array
     */
    public function getAvatarInfoAttr($value, $data)
    {
        // 默认返回值
        $result = [
            'avatar' => isset($data['avatar']) && $data['avatar'] ? FileService::getFileUrl($data['avatar']) : '',
            'thumb_avatar' => isset($data['thumb_avatar']) && $data['thumb_avatar'] ? FileService::getFileUrl($data['thumb_avatar']) : '',
            'is_reviewing' => 0 // 0=不在审核中，1=审核中
        ];

        // 注意：这里无法获取当前用户ID，所以审核状态需要在业务逻辑中单独处理
        // 或者通过其他方式传递当前用户ID
        return $result;
    }

    /**
     * @notes 背景信息获取器（包含审核状态）
     * @param $value
     * @param $data
     * @return array
     */
    public function getBackgroundInfoAttr($value, $data)
    {
        // 获取照片墙数据
        $photoWall = $this->getPhotoWallAttr($value, $data);

        // 默认返回值
        $result = [
            'background' => $photoWall['photos'] ?? [],
            'is_reviewing' => 0 // 0=不在审核中，1=审核中
        ];

        // 注意：这里无法获取当前用户ID，所以审核状态需要在业务逻辑中单独处理
        // 或者通过其他方式传递当前用户ID
        return $result;
    }

    /**
     * @notes 他人背景墙获取器（只显示已审核通过的背景墙）
     * @param $value
     * @param $data
     * @return array
     */
    public function getOtherBackgroundAttr($value, $data)
    {
        if (!isset($data['id']) || !$data['id']) {
            return [
                'background' => [],
                'is_reviewing' => 0
            ];
        }

        // 只获取已审核通过的照片墙数据
        $photoWall = UserPhotoWall::where(['user_id' => $data['id'], 'type' => 1])
            ->order('create_time desc')
            ->select()
            ->toArray();

        $photos = [];
        foreach ($photoWall as $item) {
            $photos[] = FileService::getFileUrl($item['url']);
        }

        return [
            'background' => $photos,
            'is_reviewing' => 0 // 查看他人时始终为0
        ];
    }

    /**
     * @notes 视频价格文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getVideoPriceTextAttr($value, $data)
    {
        $priceUnit = ConfigService::get('systemconfig', 'currency_name', '金币');
        $price = $data['video_price'] ?? 0;
        return $price . $priceUnit . '/min';
    }

    /**
     * @notes 语音价格文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getVoicePriceTextAttr($value, $data)
    {
        $priceUnit = ConfigService::get('systemconfig', 'currency_name', '金币');
        $price = $data['voice_price'] ?? 0;
        return $price . $priceUnit . '/min';
    }

    /**
     * @notes 等级背景获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getLevelBgAttr($value, $data)
    {
        if (!isset($data['level_id']) || !$data['level_id']) {
            return '';
        }

        $level = Level::where(['levelid' => $data['level_id']])->find();
        if (!$level) {
            return '';
        }

        // 根据性别选择不同的背景字段
        $sex = $data['sex'] ?? 1; // 默认为男性
        if ($sex == 1) {
            // 男性使用 level_bg 字段
            $levelBg = $level['level_bg'] ?? '';
        } else {
            // 女性使用 anchor_bg 字段
            $levelBg = $level['anchor_bg'] ?? '';
        }

        return $levelBg ? FileService::getFileUrl($levelBg) : '';
    }

    /**
     * @notes 在线状态文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getOnlineStatusTextAttr($value, $data)
    {
        $isOnline = $data['is_online'] ?? 0;
        $isBusy = $data['is_busy'] ?? 0;

        if ($isOnline == 1) {
            return $isBusy == 1 ? '忙碌' : '在线';
        }
        return '离线';
    }

    /**
     * 定义与邀请记录的关联关系
     */
    public function inviteRecord()
    {
        return $this->hasOne(UserInviteRecord::class, 'invite_user_id', 'id');
    }

    /**
     * 获取推荐人昵称
     * @param $value
     * @param $data
     * @return mixed|string
     */
    public function getInviterNicknameAttr($value, $data)
    {
        return $this->inviteRecord->inviter->nickname ?? null;
    }

    /**
     * 获取推荐人昵称
     * @param $value
     * @param $data
     * @return mixed|string
     */
    public function getInviterIdAttr($value, $data)
    {
        return $this->inviteRecord->inviter->id ?? null;
    }

    /**
     * 获取器获取在线状态枚举
     * @param $value
     * @param $data
     * @return mixed|string
     */
    public function getIsOnlineTextAttr($value, $data)
    {
        return UserEnum::getOnlineDesc($data['is_online']);
    }

    /**
     * 获取器获取通话状态枚举
     * @param $value
     * @param $data
     * @return mixed|string
     */
    public function getIsBusyTextAttr($value, $data)
    {
        return UserEnum::getBusyDesc($data['is_busy']);
    }

    /**
     * 获取器获取认证状态枚举
     * @param $value
     * @param $data
     * @return mixed|string
     */
    public function getIsAuthTextAttr($value, $data)
    {
        return UserEnum::getAuthDesc($data['is_auth']);
    }

    /**
     * 获取器获取认证状态枚举
     * @param $value
     * @param $data
     * @return mixed|string
     */
    public function getIsDisableTextAttr($value, $data)
    {
        return UserEnum::getDisableDesc($data['is_disable']);
    }

    /**
     * 获取器获取认证状态枚举
     * @param $value
     * @param $data
     * @return mixed|string
     */
    public function getIsRecommendTextAttr($value, $data)
    {
        return UserEnum::getRecommendDesc($data['is_recommend']);
    }

    /**
     * 获取器获取认证状态枚举
     * @param $value
     * @param $data
     * @return mixed|string
     */
    public function getIsGaoyanTextAttr($value, $data)
    {
        return UserEnum::getGaoyanDesc($data['is_gaoyan']);
    }

    /**
     * 获取器获取渠道枚举
     * @param $value
     * @param $data
     * @return mixed|string
     */
    public function getChannelTextAttr($value, $data)
    {
        return UserEnum::getChannelDesc($data['channel']);
    }

}