<?php

namespace app\applent\logic\recharge;

use app\common\logic\BaseLogic;
use app\common\model\recharge\RechargePackage;
use app\common\model\recharge\RechargeOrder;
use app\common\model\user\User;
use app\common\model\user\UserBalance;
use app\common\model\user\UserCoinLog;
use app\common\model\user\UserIncomeLog;
use app\common\model\user\CommissionIncome;
use app\common\model\user\UserInviteRecord;
use app\common\service\ConfigService;
use think\facade\Db;
use think\facade\Cache;
use app\common\model\pay\PayConfig;

/**
 * 充值业务逻辑
 * Class RechargeLogic
 * @package app\applent\logic\recharge
 */
class RechargeLogic extends BaseLogic
{
    /**
     * @notes 获取充值套餐列表
     * @param array $params
     * @return array|false
     */
    public static function getPackageList()
    {
        return RechargePackage::field('id,coin_amount,bonus_coin,price,icon')->order('sort desc')->select()->toArray();
    }
    /**
     * @notes 获取启用的支付方式列表
     */
    public static function getEnabledPayMethods()
    {
        return PayConfig::where(['status' => 1])->field('id,name,icon')->select()->toArray();
    }
    /**
     * @notes 创建充值订单
     * @param int $userId 用户ID
     * @param array $params 参数
     * @return array|false
     */
    public static function createOrder($userId, $params)
    {
        try {
            // 检查用户是否存在
            $user = User::where(['id' => $userId])->find();
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            // 检查套餐是否存在
            $package = RechargePackage::where(['id' => $params['package_id']])->find();
            if (!$package) {
                throw new \Exception('充值套餐不存在');
            }

            // 检查是否有未支付的订单（1分钟内限制重复创建）
            $cacheKey = 'recharge_order_limit_' . $userId;
            if (Cache::get($cacheKey)) {
                throw new \Exception('请勿频繁创建订单，请稍后再试');
            }
            //支付方式
            $payMethod = PayConfig::where(['id' => $params['pay_method_id'],'status' => 1])->find();
            if (empty($payMethod)) {
                throw new \Exception('支付方式不存在');
            }

            // 检查是否为首次充值，获取首冲奖励
            $isFirstOrder = self::checkIsFirstOrder($userId);
            $firstChargeReward = 0;
            if ($isFirstOrder) {
                $firstChargeReward = (int)ConfigService::get('systemconfig', 'first_charge_reward', 0);
            }

            // 创建订单数据
            $orderData = [
                'user_id' => $userId,
                'order_no' => order_sn('R'),
                'package_id' => $params['package_id'],
                'coin_amount' => $package['coin_amount'],
                'bonus_coin' => $package['bonus_coin'],
                'first_charge_reward' => $firstChargeReward, // 首冲赠送金币数量
                'total_coin' => $package['coin_amount'] + $package['bonus_coin'] + $firstChargeReward,
                'pay_amount' => $package['price'],
                'pay_method_id' => $params['pay_method_id'],
                'pay_type' => $payMethod['pay_way'],
                'create_time' => time(),
                'update_time' => time(),
            ];

            // 保存订单
            $order = RechargeOrder::create($orderData);
            if (!$order) {
                static::setError('订单创建失败');
                return false;
            }

            // 设置限制缓存（1分钟）
            Cache::set($cacheKey, 1, 60);

            return [
                'order_id' => $order['id'],
                'order_no' => $order['order_no'],
            ];

        } catch (\Exception $e) {
            static::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 处理支付回调
     * @param array $params 参数
     * @return array|false
     */
    public static function handlePayCallback($params)

    {
        try {
            Db::startTrans();
            $params['order_no'] = 'R20250723151910198515';
            // 查找订单
            $order = RechargeOrder::where(['order_no' => $params['order_no']])->find();

            if (!$order) {
                Db::rollback();
                throw new \Exception('订单不存在');
            }

            // 检查订单状态
            if ($order['pay_status'] == 1) {
                Db::rollback();
                throw new \Exception('订单已支付，请勿重复处理');
            }

            // 检查用户是否为首次充值
            $isFirstOrder = self::checkIsFirstOrder($order['user_id']);
            
            // 更新订单状态
            $updateData = [
                'pay_status'        => 1,
                'payinfo'           => json_encode($params),
                'serial_number'     => '',
                'is_first_order'    => $isFirstOrder ? 1 : 0,
                'update_time'       => time(),
                'pay_time'          => time()
            ];
            $result = RechargeOrder::where(['id' => $order['id']])->update($updateData);
            if (!$result) {
                Db::rollback();
                throw new \Exception('订单状态更新失败');
            }

            // 获取用户当前金币
            $userBalance = UserBalance::getUserBalance($order['user_id']);
            $beforeAmount = $userBalance ? $userBalance['balance'] : 0;

            // 增加用户金币（包含首冲奖励）
            $totalCoinToAdd = $order['total_coin'];
            $addBalanceResult = UserBalance::addBalance($order['user_id'], $totalCoinToAdd);
            if (!$addBalanceResult) {
                Db::rollback();
                throw new \Exception('用户金币增加失败');
            }

            // 记录金币变动日志
            $afterAmount = $beforeAmount + $totalCoinToAdd;
            $logResult = UserCoinLog::addRechargeLog(
                $order['user_id'],
                $beforeAmount,
                $afterAmount,
                $totalCoinToAdd,
                $order['order_no']
            );

            if (!$logResult) {
                Db::rollback();
                throw new \Exception('金币变动日志记录失败');
            }
            
            // 处理返佣信息
            $commissionInfo = calculate_user_commission($order['user_id'], $order['pay_amount'], 1);
            if (!empty($commissionInfo)) {
                self::handleCommissionReward($commissionInfo, $order);
            }

            //如果是首冲，增加奖励
            if ($isFirstOrder) {
                //给上级首冲奖励
                self::handleFirstChargeReward($order);
            }
            Db::commit();

            return true;

        } catch (\Exception $e) {
            Db::rollback();
            static::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 检查是否为首次充值订单
     * @param int $userId 用户ID
     * @return bool
     */
    private static function checkIsFirstOrder($userId)
    {
        // 查询该用户是否有已支付的充值订单
        $count = RechargeOrder::where(['user_id' => $userId, 'pay_status' => 1])->count();

        return $count == 0;
    }

    /**
     * @notes 处理返佣奖励
     * @param array $commissionInfo 返佣信息
     * @param array $order 订单信息
     * @return bool
     * @throws \Exception
     */
    private static function handleCommissionReward($commissionInfo, $order)
    {
        try {
            foreach ($commissionInfo as $level => $info) {
                if (empty($info['user_id']) || empty($info['commission'])) {
                    continue;
                }

                $inviterId = $info['user_id'];
                $commissionAmount = $info['commission'];
                $commissionRate = $info['rate'];
                $levelNum = $level == 'level1' ? 1 : 2;

                // 获取邀请人当前收益
                $inviterBalance = UserBalance::getUserBalance($inviterId);
                $beforeIncome = $inviterBalance ? $inviterBalance['income'] : 0;

                // 增加邀请人收益
                $addIncomeResult = UserBalance::addIncome($inviterId, $commissionAmount);
                if (!$addIncomeResult) {
                    throw new \Exception('邀请人收益增加失败');
                }

                // 记录收益变动日志
                $afterIncome = $beforeIncome + $commissionAmount;
                $incomeLogResult = UserIncomeLog::addRechargeCommissionLog(
                    $inviterId,
                    $beforeIncome,
                    $afterIncome,
                    $commissionAmount,
                    $levelNum,
                    $order['order_no'],
                    $order['user_id']
                );

                if (!$incomeLogResult) {
                    throw new \Exception('收益变动日志记录失败');
                }

                // 记录佣金收益
                // $commissionResult = CommissionIncome::addRechargeCommission(
                //     $inviterId,
                //     $order['user_id'],
                //     $levelNum,
                //     $commissionAmount,
                //     $commissionRate,
                //     $order['pay_amount'],
                //     $order['order_no']
                // );

                // if (!$commissionResult) {
                //     throw new \Exception('佣金收益记录失败');
                // }
            }

            return true;

        } catch (\Exception $e) {
            throw new \Exception('返佣处理失败：' . $e->getMessage());
        }
    }

    /**
     * @notes 处理首冲额外奖励
     * @param array $order 订单信息
     * @return bool
     * @throws \Exception
     */
    private static function handleFirstChargeReward($order)
    {
        try {
            // 获取首冲额外奖励配置
            $firstChargeReward = ConfigService::get('systemconfig', 'first_charge_higher_reward', 0);

            // 如果没有配置首冲奖励或奖励为0，直接返回
            if (!$firstChargeReward || $firstChargeReward <= 0) {
                return true;
            }

            // 查找用户的上级（邀请人）
            $inviteRecord = UserInviteRecord::where('invite_user_id', $order['user_id'])->find();
            if (!$inviteRecord) {
                return true; // 没有上级，直接返回
            }

            $inviterId = $inviteRecord->user_id;

            // 获取上级当前收益
            $inviterBalance = UserBalance::getUserBalance($inviterId);
            $beforeIncome = $inviterBalance ? $inviterBalance['income'] : 0;

            // 增加上级收益
            $addIncomeResult = UserBalance::addIncome($inviterId, $firstChargeReward);
            if (!$addIncomeResult) {
                throw new \Exception('上级首冲奖励增加失败');
            }

            // 记录收益变动日志
            $afterIncome = $beforeIncome + $firstChargeReward;
            $incomeLogResult = UserIncomeLog::addFirstChargeRewardLog(
                $inviterId,
                $beforeIncome,
                $afterIncome,
                $firstChargeReward,
                $order['order_no'],
                $order['user_id']
            );

            if (!$incomeLogResult) {
                throw new \Exception('首冲奖励收益变动日志记录失败');
            }

            return true;

        } catch (\Exception $e) {
            throw new \Exception('首冲奖励处理失败：' . $e->getMessage());
        }
    }
}
