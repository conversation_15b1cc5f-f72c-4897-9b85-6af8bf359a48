<?php
namespace app\applent\controller\user;



use app\applent\logic\user\UserLogic;
use app\applent\controller\BaseApiController;
use app\applent\validate\user\UserListValidate;
use app\applent\lists\user\UserFollowLists;
use app\applent\lists\user\UserFansLists;
use app\applent\lists\user\UserVisitorLists;
/**
 * 用户控制器
 * Class UserController
 * @package app\api\controller
 */
class UserController extends BaseApiController
{
    /**
     * @notes 获取我的个人信息
     * @return \think\response\Json
     */
    public function get_my_info()
    {
        $result = UserLogic::getMyInfo($this->userId);
        if (false === $result) {
            return $this->fail(UserLogic::getError());
        }
        return $this->success('获取成功',$result,1,0);
    }

    /**
     * @notes 获取他人信息
     * @return \think\response\Json
     */
    public function get_user_info()
    {
        $params = (new UserListValidate())->goCheck('userinfo');
        $result = UserLogic::getOtherUserInfo($params['user_id'], $this->userId);
        if (false === $result) {
            return $this->fail(UserLogic::getError());
        }
        return $this->success('获取成功',$result,1,0);
    }

    
    /**
     * @notes 获取用户形象标签列表
     * @return \think\response\Json
     */
    public function get_image_labels()
    {
        $result = UserLogic::getImageLabelList($this->userId);
        if (false === $result) {
            return $this->fail(UserLogic::getError());
        }

        return $this->success('获取成功', $result, 1, 0);
    }

    /**
     * @notes 关注/取消关注用户
     * @return \think\response\Json
     */
    public function toggle_follow()
    {
        $params = (new UserListValidate())->post()->goCheck('userFollow');
        $result = UserLogic::toggleFollow($this->userId, $params['to_user_id']);
        if (false === $result) {
            return $this->fail(UserLogic::getError());
        }
        return $this->success($result['message'], [], 1, 1);
    }

    /**
     * @notes 获取用户职业列表
     * @return \think\response\Json
     */
    public function get_career_list()
    {
        $result = UserLogic::getCareerList();
        if (false === $result) {
            return $this->fail(UserLogic::getError());
        }

        return $this->success('获取成功', $result, 1, 0);
    }

    /**
     * @notes 修改个人资料
     * @return \think\response\Json
     */
    public function update_profile()
    {
        $params = (new UserListValidate())->post()->goCheck('updateProfile');

        $result = UserLogic::updateProfile($params, $this->userId);
        if (false === $result) {
            return $this->fail(UserLogic::getError());
        }

        return $this->success('修改成功', [], 1, 1);
    }

    /**
     * @notes 用户认证提交
     * @return \think\response\Json
     */
    public function submit_auth()
    {
        $params = (new UserListValidate())->post()->goCheck('userAuth');

        $result = UserLogic::submitUserAuth($params, $this->userId);
        if (false === $result) {
            return $this->fail(UserLogic::getError());
        }

        return $this->success('提交成功，请等待审核', [], 1, 1);
    }

    /**
     * @notes 获取用户认证状态
     * @return \think\response\Json
     */
    public function get_auth_status()
    {
        $result = UserLogic::getUserAuthStatus($this->userId);
        if (false === $result) {
            return $this->fail(UserLogic::getError());
        }

        return $this->success('获取成功', $result, 1, 0);
    }

    /**
     * @notes 获取关注列表
     * @return \think\response\Json
     */
    public function get_follow_list()
    {
        $params = (new UserListValidate())->goCheck('followList');
        return $this->dataLists(new UserFollowLists());
    }

    /**
     * @notes 获取粉丝列表
     * @return \think\response\Json
     */
    public function get_fans_list()
    {
        $params = (new UserListValidate())->goCheck('fansList');
            return $this->dataLists(new UserFansLists());
    }

    /**
     * @notes 获取访客列表
     * @return \think\response\Json
     */
    public function get_visitor_list()
    {
        $params = (new UserListValidate())->goCheck('visitorList');
        return $this->dataLists(new UserVisitorLists());
    }

    /**
     * @notes 获取当前用户余额和收益信息
     * @return \think\response\Json
     */
    public function get_balance_info()
    {
        $result = UserLogic::getBalanceInfo($this->userId);
        if (false === $result) {
            return $this->fail(UserLogic::getError());
        }
        return $this->success('获取成功', $result, 1, 0);
    }
}