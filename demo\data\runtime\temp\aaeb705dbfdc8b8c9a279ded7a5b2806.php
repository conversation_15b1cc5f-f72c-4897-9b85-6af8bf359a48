<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:52:"themes/admin_simpleboot3/admin\agent\registered.html";i:1739780454;s:75:"D:\phpstudy_pro\WWW\demo\public\themes\admin_simpleboot3\public\header.html";i:1739780454;}*/ ?>
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <!-- Set render engine for 360 browser -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- HTML5 shim for IE8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <![endif]-->


    <link href="/themes/admin_simpleboot3/public/assets/themes/<?php echo cmf_get_admin_style(); ?>/bootstrap.min.css" rel="stylesheet">
    <link href="/themes/admin_simpleboot3/public/assets/simpleboot3/css/simplebootadmin.css" rel="stylesheet">
    <link href="/static/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
    <style>
        form .input-order {
            margin-bottom: 0px;
            padding: 0 2px;
            width: 42px;
            font-size: 12px;
        }

        form .input-order:focus {
            outline: none;
        }

        .table-actions {
            margin-top: 5px;
            margin-bottom: 5px;
            padding: 0px;
        }

        .table-list {
            margin-bottom: 0px;
        }

        .form-required {
            color: red;
        }
    </style>
    <script type="text/javascript">
        //全局变量
        var GV = {
            ROOT: "/",
            WEB_ROOT: "/",
            JS_ROOT: "static/js/",
            APP: '<?php echo \think\Request::instance()->module(); ?>'/*当前应用名*/
        };
    </script>
    <script src="/themes/admin_simpleboot3/public/assets/js/jquery-1.10.2.min.js"></script>
    <script src="/static/js/layer/layer.js" rel="stylesheet"></script>
    <script src="/static/js/wind.js"></script>
    <script src="/themes/admin_simpleboot3/public/assets/js/bootstrap.min.js"></script>
    <script>
        Wind.css('artDialog');
        Wind.css('layer');
        $(function () {
            $("[data-toggle='tooltip']").tooltip();
            $("li.dropdown").hover(function () {
                $(this).addClass("open");
            }, function () {
                $(this).removeClass("open");
            });
        });
    </script>
    <?php if(APP_DEBUG): ?>
        <style>
            #think_page_trace_open {
                z-index: 9999;
            }
        </style>
    <?php endif; ?>
<body>
<div class="wrap js-check-wrap">
    <ul class="nav nav-tabs">
        <li class="active"><a href="<?php echo url('agent/registered'); ?>">注册用户列表</a></li>
        <li><a href="javascript:void(0)" onclick="invitation()">绑定渠道</a></li>
    </ul>
    <form class="well form-inline margin-top-20" method="post" action="<?php echo url('agent/registered'); ?>">
        用户ID:
        <input type="text" class="form-control" name="user_id" style="width: 120px;" value="<?php echo (isset($data['user_id'] ) && ($data['user_id']  !== '')?$data['user_id'] :''); ?>" placeholder="请输入用户ID">
        渠道ID:
        <input type="text" class="form-control" name="agent_id" style="width: 120px;" value="<?php echo (isset($data['agent_id'] ) && ($data['agent_id']  !== '')?$data['agent_id'] :''); ?>" placeholder="请输入渠道">
        时间:
        <input type="text" class="form-control js-bootstrap-date" name="starttime" value="<?php echo (isset($data['starttime']) && ($data['starttime'] !== '')?$data['starttime']:''); ?>" style="width: 140px;" autocomplete="off">-
        <input type="text" class="form-control js-bootstrap-date" name="endtime" value="<?php echo (isset($data['endtime']) && ($data['endtime'] !== '')?$data['endtime']:''); ?>" style="width: 140px;" autocomplete="off"> &nbsp; &nbsp;
        注册类型：
        <select class="form-control" name="type" style="width: 140px;">
            <option value="0">全部</option>
            <option value="1" <?php if($data['type'] == 1): ?> selected  <?php endif; ?> >本账号</option>
            <option value="2" <?php if($data['type'] == 2): ?> selected  <?php endif; ?> >下级账号</option>
        </select>
        <input type="submit" class="btn btn-primary" value="搜索" />
        <a class="btn btn-danger" href="<?php echo url('agent/registered'); ?>">清空</a>
    </form>
    <table class="table table-hover table-bordered">
        <thead>
        <tr>
            <th width="50">用户UID</th>
            <th>用户昵称</th>
            <th>推广渠道</th>
            <th>来源账号</th>
            <th>时间</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody>

        <?php if(is_array($users) || $users instanceof \think\Collection || $users instanceof \think\Paginator): if( count($users)==0 ) : echo "" ;else: foreach($users as $key=>$vo): ?>
            <tr>
                <td><?php echo $vo['uid']; ?></td>
                <td><?php echo $vo['name']; ?></td>
                <td><?php echo $vo['agent_login']; ?>(<?php echo $vo['agent_id']; ?>)</td>
                <td>
                    <?php if($vo['subordinate']): ?>
                        [下级账号] <?php echo $vo['sname']; ?>(<?php echo $vo['subordinate']; ?>)
                        <?php else: ?>
                        [本账号]
                    <?php endif; ?>
                </td>
                <td><?php echo date("Y-m-d H:i:s",$vo['addtime'] ); ?></td>
                <td><a href="javascript:void(0)" onclick="save_invitation('<?php echo $vo['uid']; ?>')">更换渠道</a></td>
            </tr>
        <?php endforeach; endif; else: echo "" ;endif; ?>
        </tbody>
    </table>
    <div class="pagination"><?php echo $page; ?></div>
</div>
<script src="/static/js/admin.js"></script>
<script>
    function save_invitation(uid) {
        layer.open({
            type: 0,
            title: "绑定渠道",
            area: ['300px', '210px'],   //宽高
            shade: 0.4,   //遮罩透明度
            btn: ['确定', '取消'], //按钮组,
            content: '<div class="layui-form"><label class="layui-form-label">注册会员ID</label>'+uid+'<label class="layui-form-label">更换绑定渠道ID</label><input  type="text" id="agent_id" class="form-control" value="" ></div>',
            yes:function(index){   //点击确定回调
                layer.close(index);
                $.ajax({
                    url: "<?php echo url('agent/save_registered'); ?>",
                    type: 'get',
                    dataType: 'json',
                    data: {uid: uid,agent_id:$('#agent_id').val()},
                    success: function (data) {
                        if(data.status == 1){
                            layer.msg(data.msg,{time: 2000, icon:1},function(){
                                window.location.reload();
                            });
                        }else{
                            layer.msg(data.msg);
                        }
                    }
                });
            }
        });
    }
    function invitation() {
        layer.open({
            type: 0,
            title: "绑定渠道",
            area: ['300px', '280px'],   //宽高
            shade: 0.4,   //遮罩透明度
            btn: ['确定', '取消'], //按钮组,
            content: '<div class="layui-form"><label class="layui-form-label">注册会员ID</label><input  type="text" id="uid" class="form-control" value="" ><label class="layui-form-label">绑定渠道ID</label><input  type="text" id="agent_id" class="form-control" value="" ><div style="font-size: 13px;color: #f24444;">新绑定的用户历史充值数据会给新的代理绑定</div></div>',
            yes:function(index){   //点击确定回调
                layer.close(index);
                $.ajax({
                    url: "<?php echo url('agent/add_registered'); ?>",
                    type: 'get',
                    dataType: 'json',
                    data: {uid:$('#uid').val(),agent_id:$('#agent_id').val()},
                    success: function (data) {
                        if(data.status == 1){
                            layer.msg(data.msg,{time: 2000, icon:1},function(){
                                window.location.reload();
                            });
                        }else{
                            layer.msg(data.msg);
                        }
                    }
                });
            }
        });
    }
</script>
</body>