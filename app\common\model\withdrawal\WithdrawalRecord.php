<?php

namespace app\common\model\withdrawal;

use app\common\model\BaseModel;
use app\common\model\user\User;
use app\common\model\bank\UserBankCard;
use app\common\model\bank\UserAlipay;

/**
 * 提现记录模型
 * Class WithdrawalRecord
 * @package app\common\model\withdrawal
 */
class WithdrawalRecord extends BaseModel
{
    protected $name = 'user_withdrawal_record';

    // 提现状态
    const STATUS_PENDING = 0;  // 待审核
    const STATUS_APPROVED = 1; // 审核通过
    const STATUS_REJECTED = 2; // 审核拒绝

    // 提现方式
    const TYPE_BANK_CARD = 1; // 银行卡
    const TYPE_ALIPAY = 2;    // 支付宝

    /**
     * @notes 关联用户模型
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * @notes 关联银行卡模型
     * @return \think\model\relation\BelongsTo
     */
    public function bankCard()
    {
        return $this->belongsTo(UserBankCard::class, 'bank_card_id', 'id');
    }

    /**
     * @notes 关联支付宝模型
     * @return \think\model\relation\BelongsTo
     */
    public function alipay()
    {
        return $this->belongsTo(UserAlipay::class, 'alipay_id', 'id');
    }

    /**
     * @notes 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_PENDING => '提现中',
            self::STATUS_APPROVED => '提现成功',
            self::STATUS_REJECTED => '提现失败',
        ];
        return $statusMap[$data['status']] ?? '未知状态';
    }

    /**
     * @notes 获取提现方式文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getWithdrawalTypeTextAttr($value, $data)
    {
        $typeMap = [
            self::TYPE_BANK_CARD => '银行卡号',
            self::TYPE_ALIPAY => '支付宝账号',
        ];
        return $typeMap[$data['withdrawal_type']] ?? '未知方式';
    }

    /**
     * @notes 检查用户今日是否已提现
     * @param int $userId 用户ID
     * @return bool
     */
    public static function checkTodayWithdrawal($userId)
    {
        
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        $count = self::where('user_id', $userId)
            ->where('create_time', '>=', $todayStart)
            ->where('create_time', '<=', $todayEnd)
            ->count();

        return $count > 0;
    }

    /**
     * @notes 创建提现申请记录
     * @param array $data
     * @return WithdrawalRecord|false
     */
    public static function createWithdrawalRecord($data)
    {
        return self::create([
            'user_id' => $data['user_id'],
            'withdrawal_no' => $data['withdrawal_no'],
            'config_id' => $data['config_id'],
            'withdrawal_amount' => $data['withdrawal_amount'],
            'exchange_amount' => $data['exchange_amount'],
            'remaining_income' => $data['remaining_income'] ?? 0,
            'withdrawal_type' => $data['withdrawal_type'],
            'bank_card_id' => $data['bank_card_id'] ?? 0,
            'alipay_id' => $data['alipay_id'] ?? 0,
            'account_name' => $data['account_name'] ?? '',
            'account_number' => $data['account_number'] ?? '',
            'status' => self::STATUS_PENDING,
            'create_time' => time(),
            'update_time' => time(),
        ]);
    }

    /**
     * @notes 获取脱敏账号
     * @param $value
     * @param $data
     * @return string
     */
    public function getAccountNumberMaskedAttr($value, $data)
    {
        $accountNumber = $data['account_number'];

        if (empty($accountNumber)) {
            return '';
        }

        $length = strlen($accountNumber);
        if ($length <= 4) {
            return $accountNumber;
        }

        // 显示前3位和后4位，中间用*号代替
        $start = substr($accountNumber, 0, 3);
        $end = substr($accountNumber, -4);
        $middle = str_repeat('*', min(4, $length - 7));

        return $start . $middle . $end;
    }



    /**
     * @notes 按月份搜索器
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchDateAttr($query, $value, $data)
    {
        if (!empty($value)) {
            // 解析 YYYY-MM 格式
            $parts = explode('-', $value);
            if (count($parts) == 2) {
                $year = (int)$parts[0];
                $month = (int)$parts[1];

                // 计算该月的开始和结束时间
                $startTime = strtotime($year . '-' . sprintf('%02d', $month) . '-01 00:00:00');
                $endTime = strtotime(date('Y-m-t 23:59:59', $startTime)); // 该月最后一天

                $query->where('create_time', 'between', [$startTime, $endTime]);
            }
        } else {
            // 如果没有传入日期，默认查找最近一周的数据
            $startTime = strtotime('-7 days');
            $endTime = time();
            $query->where('create_time', 'between', [$startTime, $endTime]);
        }
    }

    /**
     * @notes 状态搜索器
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchStatusAttr($query, $value, $data)
    {
        if ($value !== '' && $value !== null && $value != 0) {
            // 0=全部，不添加条件
            // 1=审核中，2=提现成功，3=提现失败
            $statusMap = [
                1 => self::STATUS_PENDING,    // 审核中
                2 => self::STATUS_APPROVED,   // 提现成功
                3 => self::STATUS_REJECTED,   // 提现失败
            ];
            if (isset($statusMap[$value])) {
                $query->where('status', '=', $statusMap[$value]);
            }
        }
    }
}
